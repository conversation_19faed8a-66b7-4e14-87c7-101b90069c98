#!/usr/bin/env python3
"""
简单测试兑换码系统
"""

from redemption_system import RedemptionSystem
import json

def test_redemption_system():
    """测试兑换码系统"""
    print("测试兑换码系统...")
    
    # 创建兑换码系统实例
    rs = RedemptionSystem()
    
    # 测试获取统计信息
    print("\n1. 测试获取统计信息:")
    stats = rs.get_statistics()
    print(f"统计信息: {json.dumps(stats, indent=2, ensure_ascii=False)}")
    
    # 测试获取所有兑换码
    print("\n2. 测试获取所有兑换码:")
    codes = rs.get_all_codes()
    print(f"兑换码数量: {len(codes)}")
    if codes:
        print(f"第一个兑换码: {json.dumps(codes[0], indent=2, ensure_ascii=False)}")
    
    # 测试获取使用记录
    print("\n3. 测试获取使用记录:")
    records = rs.get_usage_records()
    print(f"使用记录数量: {len(records)}")
    if records:
        print(f"第一条记录: {json.dumps(records[0], indent=2, ensure_ascii=False)}")
    
    # 测试创建兑换码
    print("\n4. 测试创建兑换码:")
    try:
        new_codes = rs.create_redemption_codes(
            code_type='one_time',
            points=10,
            count=1,
            expire_days=30,
            description='测试兑换码'
        )
        print(f"成功创建兑换码: {new_codes}")
    except Exception as e:
        print(f"创建兑换码失败: {e}")
    
    print("\n测试完成!")

if __name__ == '__main__':
    test_redemption_system()
