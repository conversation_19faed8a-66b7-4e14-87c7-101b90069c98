#!/usr/bin/env python3
"""
直接测试API接口
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from auth import UserManager
from redemption_system import RedemptionSystem
import json

def test_api_directly():
    """直接测试API接口"""
    print("直接测试API接口...")
    
    with app.test_client() as client:
        # 模拟登录
        print("\n1. 模拟登录...")
        with client.session_transaction() as sess:
            sess['username'] = 'admin'
        
        # 测试统计接口
        print("\n2. 测试统计接口...")
        response = client.get('/admin/redemption_statistics')
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.get_json()}")
        
        # 测试兑换码列表接口
        print("\n3. 测试兑换码列表接口...")
        response = client.get('/admin/redemption_codes')
        print(f"状态码: {response.status_code}")
        data = response.get_json()
        if data and data.get('success'):
            print(f"兑换码数量: {len(data.get('codes', []))}")
        else:
            print(f"响应: {data}")
        
        # 测试使用记录接口
        print("\n4. 测试使用记录接口...")
        response = client.get('/admin/redemption_usage_records')
        print(f"状态码: {response.status_code}")
        data = response.get_json()
        if data and data.get('success'):
            print(f"使用记录数量: {len(data.get('records', []))}")
        else:
            print(f"响应: {data}")
        
        # 测试创建兑换码接口
        print("\n5. 测试创建兑换码接口...")
        create_data = {
            'type': 'one_time',
            'points': 10,
            'count': 1,
            'expire_days': 30,
            'description': 'API直接测试兑换码'
        }
        response = client.post('/admin/create_redemption_codes', 
                             json=create_data,
                             content_type='application/json')
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.get_json()}")

def test_user_manager():
    """测试用户管理器"""
    print("\n\n测试用户管理器...")
    
    user_manager = UserManager()
    admin_user = user_manager.get_user('admin')
    
    print(f"Admin用户信息: {admin_user}")
    print(f"是否为管理员: {admin_user.get('is_admin', False) if admin_user else False}")

if __name__ == '__main__':
    test_user_manager()
    test_api_directly()
