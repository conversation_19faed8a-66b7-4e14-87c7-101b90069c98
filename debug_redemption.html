<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>兑换码管理调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .error { color: red; }
        .success { color: green; }
        .loading { color: blue; }
        button { margin: 5px; padding: 10px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>兑换码管理功能调试</h1>
    
    <div class="section">
        <h2>1. 测试兑换码统计API</h2>
        <button onclick="testStatistics()">测试统计接口</button>
        <div id="statisticsResult"></div>
    </div>
    
    <div class="section">
        <h2>2. 测试兑换码列表API</h2>
        <button onclick="testCodesList()">测试列表接口</button>
        <div id="codesListResult"></div>
    </div>
    
    <div class="section">
        <h2>3. 测试使用记录API</h2>
        <button onclick="testUsageRecords()">测试使用记录接口</button>
        <div id="usageRecordsResult"></div>
    </div>
    
    <div class="section">
        <h2>4. 测试生成兑换码API</h2>
        <button onclick="testCreateCode()">测试生成接口</button>
        <div id="createCodeResult"></div>
    </div>
    
    <div class="section">
        <h2>5. 检查用户登录状态</h2>
        <button onclick="checkLoginStatus()">检查登录状态</button>
        <div id="loginStatusResult"></div>
    </div>

    <div class="section">
        <h2>6. 浏览器控制台日志</h2>
        <p>请打开浏览器开发者工具的控制台查看详细错误信息</p>
        <div id="consoleLog"></div>
    </div>

    <script>
        function logResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'loading';
            element.innerHTML = `<div class="${className}">${message}</div>`;
            console.log(`[${type.toUpperCase()}] ${elementId}: ${message}`);
        }

        function testStatistics() {
            logResult('statisticsResult', '正在测试统计接口...', 'loading');
            
            fetch('/admin/redemption_statistics')
                .then(response => {
                    console.log('统计接口响应状态:', response.status);
                    console.log('统计接口响应头:', response.headers);
                    return response.json();
                })
                .then(data => {
                    console.log('统计接口响应数据:', data);
                    if (data.success) {
                        logResult('statisticsResult', 
                            `✓ 统计接口正常<br>
                            <pre>${JSON.stringify(data.statistics, null, 2)}</pre>`, 
                            'success');
                    } else {
                        logResult('statisticsResult', `✗ 统计接口失败: ${data.message}`, 'error');
                    }
                })
                .catch(error => {
                    console.error('统计接口错误:', error);
                    logResult('statisticsResult', `✗ 统计接口请求失败: ${error.message}`, 'error');
                });
        }

        function testCodesList() {
            logResult('codesListResult', '正在测试列表接口...', 'loading');
            
            fetch('/admin/redemption_codes')
                .then(response => {
                    console.log('列表接口响应状态:', response.status);
                    console.log('列表接口响应头:', response.headers);
                    return response.json();
                })
                .then(data => {
                    console.log('列表接口响应数据:', data);
                    if (data.success) {
                        logResult('codesListResult', 
                            `✓ 列表接口正常<br>
                            兑换码数量: ${data.codes.length}<br>
                            <pre>${JSON.stringify(data.codes.slice(0, 2), null, 2)}</pre>`, 
                            'success');
                    } else {
                        logResult('codesListResult', `✗ 列表接口失败: ${data.message}`, 'error');
                    }
                })
                .catch(error => {
                    console.error('列表接口错误:', error);
                    logResult('codesListResult', `✗ 列表接口请求失败: ${error.message}`, 'error');
                });
        }

        function testUsageRecords() {
            logResult('usageRecordsResult', '正在测试使用记录接口...', 'loading');
            
            fetch('/admin/redemption_usage_records')
                .then(response => {
                    console.log('使用记录接口响应状态:', response.status);
                    console.log('使用记录接口响应头:', response.headers);
                    return response.json();
                })
                .then(data => {
                    console.log('使用记录接口响应数据:', data);
                    if (data.success) {
                        logResult('usageRecordsResult', 
                            `✓ 使用记录接口正常<br>
                            记录数量: ${data.records.length}<br>
                            <pre>${JSON.stringify(data.records.slice(0, 2), null, 2)}</pre>`, 
                            'success');
                    } else {
                        logResult('usageRecordsResult', `✗ 使用记录接口失败: ${data.message}`, 'error');
                    }
                })
                .catch(error => {
                    console.error('使用记录接口错误:', error);
                    logResult('usageRecordsResult', `✗ 使用记录接口请求失败: ${error.message}`, 'error');
                });
        }

        function testCreateCode() {
            logResult('createCodeResult', '正在测试生成接口...', 'loading');
            
            const testData = {
                type: 'one_time',
                points: 10,
                count: 1,
                expire_days: 30,
                description: '调试测试兑换码'
            };
            
            fetch('/admin/create_redemption_codes', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(testData)
            })
                .then(response => {
                    console.log('生成接口响应状态:', response.status);
                    console.log('生成接口响应头:', response.headers);
                    return response.json();
                })
                .then(data => {
                    console.log('生成接口响应数据:', data);
                    if (data.success) {
                        logResult('createCodeResult', 
                            `✓ 生成接口正常<br>
                            ${data.message}<br>
                            生成的兑换码: ${data.codes.join(', ')}`, 
                            'success');
                    } else {
                        logResult('createCodeResult', `✗ 生成接口失败: ${data.message}`, 'error');
                    }
                })
                .catch(error => {
                    console.error('生成接口错误:', error);
                    logResult('createCodeResult', `✗ 生成接口请求失败: ${error.message}`, 'error');
                });
        }

        function checkLoginStatus() {
            logResult('loginStatusResult', '正在检查登录状态...', 'loading');

            fetch('/user_info')
                .then(response => {
                    console.log('用户信息接口响应状态:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('用户信息接口响应数据:', data);
                    if (data.success) {
                        logResult('loginStatusResult',
                            `✓ 用户已登录<br>
                            用户名: ${data.user.username}<br>
                            是否管理员: ${data.user.is_admin ? '是' : '否'}<br>
                            积分: ${data.user.points}`,
                            'success');
                    } else {
                        logResult('loginStatusResult', `✗ 用户未登录或获取信息失败: ${data.message}`, 'error');
                    }
                })
                .catch(error => {
                    console.error('用户信息接口错误:', error);
                    logResult('loginStatusResult', `✗ 获取用户信息失败: ${error.message}`, 'error');
                });
        }

        // 捕获控制台错误
        window.onerror = function(message, source, lineno, colno, error) {
            const errorMsg = `JavaScript错误: ${message} (${source}:${lineno}:${colno})`;
            console.error(errorMsg, error);
            const consoleDiv = document.getElementById('consoleLog');
            if (consoleDiv) {
                consoleDiv.innerHTML += `<div class="error">${errorMsg}</div>`;
            }
        };

        // 页面加载时自动测试
        window.onload = function() {
            console.log('开始自动测试兑换码管理功能...');
            setTimeout(checkLoginStatus, 200);
            setTimeout(testStatistics, 500);
            setTimeout(testCodesList, 1000);
            setTimeout(testUsageRecords, 1500);
        };
    </script>
</body>
</html>
