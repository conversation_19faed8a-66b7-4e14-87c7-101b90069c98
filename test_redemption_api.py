#!/usr/bin/env python3
"""
测试兑换码管理API接口
"""

import requests
import json

# 测试配置
BASE_URL = 'http://localhost:7799'
TEST_USERNAME = 'admin'
TEST_PASSWORD = 'admin123'

def test_login():
    """测试登录"""
    print("测试登录...")
    session = requests.Session()
    
    # 登录
    login_data = {
        'username': TEST_USERNAME,
        'password': TEST_PASSWORD
    }
    
    response = session.post(f'{BASE_URL}/login', json=login_data)
    print(f"登录响应状态码: {response.status_code}")
    print(f"登录响应内容: {response.text}")
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print("✓ 登录成功")
            return session
        else:
            print(f"✗ 登录失败: {result.get('message')}")
            return None
    else:
        print(f"✗ 登录请求失败: {response.status_code}")
        return None

def test_redemption_statistics(session):
    """测试兑换码统计接口"""
    print("\n测试兑换码统计接口...")
    
    response = session.get(f'{BASE_URL}/admin/redemption_statistics')
    print(f"统计接口响应状态码: {response.status_code}")
    print(f"统计接口响应内容: {response.text}")
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print("✓ 兑换码统计接口正常")
            stats = result.get('statistics', {})
            print(f"  - 总兑换码数: {stats.get('total_codes', 0)}")
            print(f"  - 活跃兑换码数: {stats.get('active_codes', 0)}")
            print(f"  - 已使用兑换码数: {stats.get('total_codes_used', 0)}")
            print(f"  - 已分发积分: {stats.get('total_points_distributed', 0)}")
        else:
            print(f"✗ 兑换码统计接口失败: {result.get('message')}")
    else:
        print(f"✗ 兑换码统计接口请求失败: {response.status_code}")

def test_redemption_usage_records(session):
    """测试兑换码使用记录接口"""
    print("\n测试兑换码使用记录接口...")
    
    response = session.get(f'{BASE_URL}/admin/redemption_usage_records')
    print(f"使用记录接口响应状态码: {response.status_code}")
    print(f"使用记录接口响应内容: {response.text}")
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print("✓ 兑换码使用记录接口正常")
            records = result.get('records', [])
            print(f"  - 使用记录数量: {len(records)}")
            if records:
                print("  - 最近3条记录:")
                for i, record in enumerate(records[:3]):
                    print(f"    {i+1}. {record.get('username')} 使用 {record.get('code')} 获得 {record.get('points')} 积分")
        else:
            print(f"✗ 兑换码使用记录接口失败: {result.get('message')}")
    else:
        print(f"✗ 兑换码使用记录接口请求失败: {response.status_code}")

def test_redemption_codes(session):
    """测试兑换码列表接口"""
    print("\n测试兑换码列表接口...")
    
    response = session.get(f'{BASE_URL}/admin/redemption_codes')
    print(f"兑换码列表接口响应状态码: {response.status_code}")
    print(f"兑换码列表接口响应内容: {response.text[:500]}...")  # 只显示前500字符
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print("✓ 兑换码列表接口正常")
            codes = result.get('codes', [])
            print(f"  - 兑换码数量: {len(codes)}")
            if codes:
                print("  - 前3个兑换码:")
                for i, code in enumerate(codes[:3]):
                    print(f"    {i+1}. {code.get('code')} - {code.get('points')}积分 - {'有效' if code.get('is_active') else '无效'}")
        else:
            print(f"✗ 兑换码列表接口失败: {result.get('message')}")
    else:
        print(f"✗ 兑换码列表接口请求失败: {response.status_code}")

def test_create_redemption_code(session):
    """测试创建兑换码接口"""
    print("\n测试创建兑换码接口...")
    
    create_data = {
        'type': 'one_time',
        'points': 10,
        'count': 1,
        'expire_days': 30,
        'description': 'API测试兑换码'
    }
    
    response = session.post(f'{BASE_URL}/admin/create_redemption_codes', json=create_data)
    print(f"创建兑换码接口响应状态码: {response.status_code}")
    print(f"创建兑换码接口响应内容: {response.text}")
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print("✓ 创建兑换码接口正常")
            codes = result.get('codes', [])
            if codes:
                print(f"  - 创建的兑换码: {codes[0]}")
        else:
            print(f"✗ 创建兑换码接口失败: {result.get('message')}")
    else:
        print(f"✗ 创建兑换码接口请求失败: {response.status_code}")

def main():
    """主测试函数"""
    print("开始测试兑换码管理API接口...")
    
    # 登录
    session = test_login()
    if not session:
        print("登录失败，无法继续测试")
        return
    
    # 测试各个接口
    test_redemption_statistics(session)
    test_redemption_usage_records(session)
    test_redemption_codes(session)
    test_create_redemption_code(session)
    
    print("\n测试完成！")

if __name__ == '__main__':
    main()
